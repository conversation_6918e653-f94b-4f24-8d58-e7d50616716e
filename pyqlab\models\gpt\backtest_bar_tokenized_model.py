"""
基于BarTokenizer训练模型的回测系统

这个脚本专门用于回测使用BarTokenizer训练的BarGpt4模型，
支持ONNX格式模型和PyTorch Lightning checkpoint模型。

主要特性：
1. 支持加载BarTokenizer进行数据预处理
2. 支持ONNX和PyTorch模型格式
3. 集成信号生成策略
4. 提供详细的回测分析和可视化
5. 支持多种交易策略和风险管理
"""

import os
import sys
import json
import pickle
import torch
import numpy as np
import pandas as pd
from argparse import ArgumentParser
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from pyqlab.models.gpt.train_bar_gpt4_with_tokenizer import BarGpt4LightningModule
from pyqlab.models.gpt.onnx_model_wrapper import OnnxModelWrapper
from pyqlab.models.gpt2.signal_generator import (
    SignalGenerator, ThresholdDirectionStrategy, TopKStrategy,
    StatisticalMomentumStrategy, WeightedEnsembleStrategy
)

# 尝试导入ONNX相关模块
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("警告: onnxruntime未安装，无法使用ONNX模型")


class BarTokenizedModelBacktester:
    """基于BarTokenizer训练模型的回测器"""

    def __init__(self, model, tokenizer, initial_capital=10000.0, device=None,
                 signal_type='threshold', leverage=1.0):
        """
        初始化回测器

        Args:
            model: 训练好的模型（PyTorch或ONNX包装器）
            tokenizer: BarTokenizer实例
            initial_capital: 初始资金
            device: 计算设备
            signal_type: 信号生成策略类型
            leverage: 交易杠杆倍数
        """
        self.model = model
        self.tokenizer = tokenizer
        self.initial_capital = initial_capital
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.leverage = max(1.0, leverage)

        # 将模型移动到指定设备（如果是PyTorch模型）
        if hasattr(self.model, 'to'):
            self.model.to(self.device)

        # 设置模型为推理模式
        if hasattr(self.model, 'eval'):
            self.model.eval()
        elif hasattr(self.model, 'inference_mode'):
            self.model.inference_mode()

        # 创建信号生成器
        self.signal_generator = self._create_signal_generator(signal_type)

        print(f"回测器初始化完成:")
        print(f"  初始资金: {self.initial_capital:,.2f}")
        print(f"  杠杆倍数: {self.leverage}x")
        print(f"  信号策略: {signal_type}")
        print(f"  计算设备: {self.device}")

    def _create_signal_generator(self, signal_type):
        """创建信号生成器"""
        if signal_type == 'threshold':
            strategy = ThresholdDirectionStrategy(threshold=0.6)
        elif signal_type == 'topk':
            strategy = TopKStrategy(k=5, min_prob=0.3)
        elif signal_type == 'momentum':
            strategy = StatisticalMomentumStrategy(momentum_threshold=0.2)
        elif signal_type == 'ensemble':
            strategies = [
                (ThresholdDirectionStrategy(threshold=0.6), 0.5),
                (TopKStrategy(k=5, min_prob=0.3), 0.3),
                (StatisticalMomentumStrategy(momentum_threshold=0.2), 0.2)
            ]
            strategy = WeightedEnsembleStrategy(strategies)
        else:
            # 默认使用阈值策略
            strategy = ThresholdDirectionStrategy(threshold=0.6)

        return SignalGenerator(strategy)

    def _prepare_input_data(self, df, seq_len, start_idx):
        """准备模型输入数据"""
        # 提取序列数据
        end_idx = start_idx + seq_len
        if end_idx > len(df):
            return None, None, None

        sequence_df = df.iloc[start_idx:end_idx].copy()

        # 使用tokenizer进行token化
        try:
            tokens = self.tokenizer.transform(sequence_df)
            if len(tokens) != seq_len:
                # 如果token数量不匹配，进行填充或截断
                if len(tokens) < seq_len:
                    # 填充
                    tokens = np.pad(tokens, (0, seq_len - len(tokens)), mode='constant', constant_values=0)
                else:
                    # 截断
                    tokens = tokens[:seq_len]
        except Exception as e:
            print(f"Token化失败: {e}")
            return None, None, None

        # 创建代码序列（假设单一证券，使用固定代码）
        code = np.zeros(seq_len, dtype=np.int32)

        # 创建时间特征
        x_mark = self._create_time_features(sequence_df, seq_len)

        # 转换为张量
        code_tensor = torch.tensor(code, dtype=torch.long).unsqueeze(0)  # [1, seq_len]
        x_tensor = torch.tensor(tokens, dtype=torch.long).unsqueeze(0)   # [1, seq_len]
        x_mark_tensor = torch.tensor(x_mark, dtype=torch.float32).unsqueeze(0)  # [1, seq_len, n_features]

        # 移动到设备
        if self.device != 'cpu' and hasattr(code_tensor, 'to'):
            code_tensor = code_tensor.to(self.device)
            x_tensor = x_tensor.to(self.device)
            x_mark_tensor = x_mark_tensor.to(self.device)

        return code_tensor, x_tensor, x_mark_tensor

    def _create_time_features(self, df, seq_len):
        """创建时间特征"""
        # 简化的时间特征创建
        n_time_features = 5  # 根据模型配置调整

        if 'datetime' in df.columns:
            # 如果有datetime列，提取时间特征
            time_features = []
            for _, row in df.iterrows():
                dt = pd.to_datetime(row['datetime'])
                features = [
                    dt.hour / 24.0,           # 小时特征
                    dt.minute / 60.0,         # 分钟特征
                    dt.weekday() / 7.0,       # 星期特征
                    dt.day / 31.0,            # 日期特征
                    dt.month / 12.0           # 月份特征
                ]
                time_features.append(features)

            time_features = np.array(time_features)

            # 确保形状正确
            if time_features.shape[0] != seq_len:
                if time_features.shape[0] < seq_len:
                    # 填充
                    padding = np.zeros((seq_len - time_features.shape[0], n_time_features))
                    time_features = np.vstack([time_features, padding])
                else:
                    # 截断
                    time_features = time_features[:seq_len]
        else:
            # 如果没有datetime列，创建默认时间特征
            time_features = np.zeros((seq_len, n_time_features))

        return time_features

    def _predict_next_token(self, code, x, x_mark, temperature=1.0, top_k=None):
        """预测下一个token"""
        try:
            if isinstance(self.model, OnnxModelWrapper):
                # ONNX模型推理 - 使用__call__方法
                logits, _ = self.model(code, x, x_mark)
            else:
                # PyTorch模型推理
                with torch.no_grad():
                    if hasattr(self.model, 'model'):
                        # Lightning模块
                        logits, _ = self.model.model(code, x, x_mark)
                    else:
                        # 直接的BarGpt4模型
                        logits, _ = self.model(code, x, x_mark)

            # 确保logits是张量
            if not isinstance(logits, torch.Tensor):
                logits = torch.tensor(logits)

            # 如果logits是3D张量 (batch_size, seq_len, vocab_size)，取最后一个时间步
            if len(logits.shape) == 3:
                logits = logits[:, -1, :]  # 取最后一个时间步的预测
            elif len(logits.shape) == 2:
                # 如果是2D张量，假设已经是最后一个时间步的预测
                pass
            else:
                print(f"警告: 意外的logits形状: {logits.shape}")

            # 应用温度
            if temperature != 1.0:
                logits = logits / temperature

            # 应用top-k过滤
            if top_k is not None and top_k > 0:
                top_k = min(top_k, logits.size(-1))
                top_k_logits, top_k_indices = torch.topk(logits, top_k, dim=-1)
                # 创建mask
                mask = torch.full_like(logits, float('-inf'))
                mask.scatter_(-1, top_k_indices, top_k_logits)
                logits = mask

            return logits

        except Exception as e:
            print(f"预测失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _generate_trading_signal(self, logits):
        """生成交易信号"""
        if logits is None:
            return {'signal': 'HOLD', 'confidence': 0.0}

        try:
            # 为BarTokenizer创建兼容的tokenizer包装器
            tokenizer_wrapper = self._create_tokenizer_wrapper()

            # 使用信号生成器生成信号
            signal = self.signal_generator.generate_signal(logits, tokenizer_wrapper)
            return signal
        except Exception as e:
            print(f"信号生成失败: {e}")
            return {'signal': 'HOLD', 'confidence': 0.0}

    def _create_tokenizer_wrapper(self):
        """为BarTokenizer创建兼容的包装器"""
        class TokenizerWrapper:
            def __init__(self, bar_tokenizer):
                self.bar_tokenizer = bar_tokenizer
                self.vocab_size = bar_tokenizer.get_vocab_size()

                # 创建简单的idx2token映射
                self.idx2token = {i: f"token_{i}" for i in range(self.vocab_size)}

            def decode(self, token_ids):
                """解码token IDs"""
                if isinstance(token_ids, (list, tuple)):
                    return [self.idx2token.get(tid, f"unk_{tid}") for tid in token_ids]
                else:
                    return self.idx2token.get(token_ids, f"unk_{token_ids}")

        return TokenizerWrapper(self.tokenizer)

    def backtest(self, df, seq_len=30, commission=0.001, threshold=0.6,
                stop_loss=None, take_profit=None, temperature=1.0,
                top_k=None, print_interval=100):
        """
        执行回测

        Args:
            df: 包含OHLCV数据的DataFrame
            seq_len: 序列长度
            commission: 交易手续费率
            threshold: 交易信号阈值
            stop_loss: 止损比例
            take_profit: 止盈比例
            temperature: 温度参数
            top_k: Top-K采样参数
            print_interval: 打印间隔

        Returns:
            回测结果字典
        """
        print(f"\n=== 开始回测 ===")
        print(f"数据长度: {len(df)}")
        print(f"序列长度: {seq_len}")
        print(f"手续费率: {commission:.4f}")

        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"数据缺少必要的列: {col}")

        # 重置索引
        df = df.reset_index(drop=True)

        # 初始化回测状态
        capital = self.initial_capital
        position = 0  # 0: 空仓, 1: 多头, -1: 空头
        entry_price = 0
        trades = []
        equity_curve = []
        positions_history = []
        signals_history = []

        # 开始回测循环
        total_steps = len(df) - seq_len
        for i in range(total_steps):
            current_idx = seq_len + i

            # 准备输入数据
            code, x, x_mark = self._prepare_input_data(df, seq_len, i)
            if code is None:
                continue

            # 预测下一个token
            logits = self._predict_next_token(code, x, x_mark, temperature, top_k)

            # 生成交易信号
            signal_info = self._generate_trading_signal(logits)
            signal = signal_info.get('signal', 'HOLD')
            confidence = signal_info.get('confidence', 0.0)

            # 记录信号
            signals_history.append({
                'index': current_idx,
                'signal': signal,
                'confidence': confidence,
                'datetime': df.iloc[current_idx].get('datetime', current_idx)
            })

            # 获取当前价格
            current_price = df.iloc[current_idx]['close']

            # 执行交易逻辑
            trade_executed = False

            # 检查止损止盈
            if position != 0 and entry_price > 0:
                price_change = (current_price - entry_price) / entry_price

                # 考虑持仓方向
                if position == 1:  # 多头
                    pnl_ratio = price_change
                elif position == -1:  # 空头
                    pnl_ratio = -price_change
                else:
                    pnl_ratio = 0

                # 检查止损
                if stop_loss and pnl_ratio <= -stop_loss:
                    # 平仓
                    trade_value = abs(position) * current_price * self.leverage
                    commission_cost = trade_value * commission

                    if position == 1:
                        # 平多
                        capital += (current_price - entry_price) * abs(position) * self.leverage - commission_cost
                    else:
                        # 平空
                        capital += (entry_price - current_price) * abs(position) * self.leverage - commission_cost

                    trades.append({
                        'type': 'STOP_LOSS',
                        'direction': 'SELL' if position > 0 else 'BUY',
                        'price': current_price,
                        'quantity': abs(position),
                        'datetime': df.iloc[current_idx].get('datetime', current_idx),
                        'pnl': pnl_ratio,
                        'capital': capital
                    })

                    position = 0
                    entry_price = 0
                    trade_executed = True

                # 检查止盈
                elif take_profit and pnl_ratio >= take_profit:
                    # 平仓
                    trade_value = abs(position) * current_price * self.leverage
                    commission_cost = trade_value * commission

                    if position == 1:
                        # 平多
                        capital += (current_price - entry_price) * abs(position) * self.leverage - commission_cost
                    else:
                        # 平空
                        capital += (entry_price - current_price) * abs(position) * self.leverage - commission_cost

                    trades.append({
                        'type': 'TAKE_PROFIT',
                        'direction': 'SELL' if position > 0 else 'BUY',
                        'price': current_price,
                        'quantity': abs(position),
                        'datetime': df.iloc[current_idx].get('datetime', current_idx),
                        'pnl': pnl_ratio,
                        'capital': capital
                    })

                    position = 0
                    entry_price = 0
                    trade_executed = True

            # 如果没有执行止损止盈，根据信号执行交易
            if not trade_executed and confidence >= threshold:
                if signal == 'BUY' and position <= 0:
                    # 开多或平空后开多
                    if position < 0:
                        # 先平空
                        trade_value = abs(position) * current_price * self.leverage
                        commission_cost = trade_value * commission
                        capital += (entry_price - current_price) * abs(position) * self.leverage - commission_cost

                        trades.append({
                            'type': 'CLOSE_SHORT',
                            'direction': 'BUY',
                            'price': current_price,
                            'quantity': abs(position),
                            'datetime': df.iloc[current_idx].get('datetime', current_idx),
                            'pnl': (entry_price - current_price) / entry_price,
                            'capital': capital
                        })

                    # 开多
                    position_size = capital * 0.95 / current_price / self.leverage  # 使用95%资金
                    trade_value = position_size * current_price * self.leverage
                    commission_cost = trade_value * commission
                    capital -= commission_cost

                    position = position_size
                    entry_price = current_price

                    trades.append({
                        'type': 'OPEN_LONG',
                        'direction': 'BUY',
                        'price': current_price,
                        'quantity': position_size,
                        'datetime': df.iloc[current_idx].get('datetime', current_idx),
                        'pnl': 0,
                        'capital': capital
                    })

                elif signal == 'SELL' and position >= 0:
                    # 开空或平多后开空
                    if position > 0:
                        # 先平多
                        trade_value = abs(position) * current_price * self.leverage
                        commission_cost = trade_value * commission
                        capital += (current_price - entry_price) * abs(position) * self.leverage - commission_cost

                        trades.append({
                            'type': 'CLOSE_LONG',
                            'direction': 'SELL',
                            'price': current_price,
                            'quantity': abs(position),
                            'datetime': df.iloc[current_idx].get('datetime', current_idx),
                            'pnl': (current_price - entry_price) / entry_price,
                            'capital': capital
                        })

                    # 开空
                    position_size = capital * 0.95 / current_price / self.leverage  # 使用95%资金
                    trade_value = position_size * current_price * self.leverage
                    commission_cost = trade_value * commission
                    capital -= commission_cost

                    position = -position_size
                    entry_price = current_price

                    trades.append({
                        'type': 'OPEN_SHORT',
                        'direction': 'SELL',
                        'price': current_price,
                        'quantity': position_size,
                        'datetime': df.iloc[current_idx].get('datetime', current_idx),
                        'pnl': 0,
                        'capital': capital
                    })

            # 计算当前权益
            if position != 0 and entry_price > 0:
                unrealized_pnl = 0
                if position > 0:  # 多头
                    unrealized_pnl = (current_price - entry_price) * position * self.leverage
                elif position < 0:  # 空头
                    unrealized_pnl = (entry_price - current_price) * abs(position) * self.leverage

                current_equity = capital + unrealized_pnl
            else:
                current_equity = capital

            # 记录权益和持仓
            equity_curve.append({
                'index': current_idx,
                'datetime': df.iloc[current_idx].get('datetime', current_idx),
                'price': current_price,
                'capital': capital,
                'equity': current_equity,
                'position': position,
                'entry_price': entry_price
            })

            positions_history.append({
                'index': current_idx,
                'position': position,
                'entry_price': entry_price,
                'current_price': current_price
            })

            # 打印进度
            if (i + 1) % print_interval == 0:
                print(f"进度: {i+1}/{total_steps} ({(i+1)/total_steps*100:.1f}%), "
                      f"当前权益: {current_equity:,.2f}, "
                      f"持仓: {position:.2f}, "
                      f"最新信号: {signal}({confidence:.3f})")

        # 最终平仓
        if position != 0:
            final_price = df.iloc[-1]['close']
            trade_value = abs(position) * final_price * self.leverage
            commission_cost = trade_value * commission

            if position > 0:
                capital += (final_price - entry_price) * position * self.leverage - commission_cost
                final_pnl = (final_price - entry_price) / entry_price
            else:
                capital += (entry_price - final_price) * abs(position) * self.leverage - commission_cost
                final_pnl = (entry_price - final_price) / entry_price

            trades.append({
                'type': 'FINAL_CLOSE',
                'direction': 'SELL' if position > 0 else 'BUY',
                'price': final_price,
                'quantity': abs(position),
                'datetime': df.iloc[-1].get('datetime', len(df)-1),
                'pnl': final_pnl,
                'capital': capital
            })

        # 计算回测统计
        final_equity = capital
        total_return = (final_equity - self.initial_capital) / self.initial_capital

        print(f"\n=== 回测完成 ===")
        print(f"初始资金: {self.initial_capital:,.2f}")
        print(f"最终权益: {final_equity:,.2f}")
        print(f"总收益率: {total_return:.2%}")
        print(f"总交易次数: {len(trades)}")

        return {
            'initial_capital': self.initial_capital,
            'final_equity': final_equity,
            'total_return': total_return,
            'trades': trades,
            'equity_curve': equity_curve,
            'positions_history': positions_history,
            'signals_history': signals_history,
            'leverage': self.leverage
        }


def load_tokenizer(tokenizer_path):
    """加载BarTokenizer"""
    print(f"加载Tokenizer: {tokenizer_path}")

    if not os.path.exists(tokenizer_path):
        raise FileNotFoundError(f"Tokenizer文件不存在: {tokenizer_path}")

    try:
        # 首先尝试使用BarTokenizer.load_model方法
        try:
            from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
            tokenizer = BarTokenizer.load_model(tokenizer_path)

            print(f"✅ Tokenizer加载成功")
            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  特征列表: {tokenizer.features}")
            print(f"  映射策略: {tokenizer.mapping_strategy_name}")

            return tokenizer

        except Exception as e1:
            # 如果失败，尝试直接加载pickle文件（可能是字典格式）
            print(f"尝试使用BarTokenizer.load_model失败: {e1}")
            print(f"尝试直接加载pickle文件...")

            with open(tokenizer_path, 'rb') as f:
                data = pickle.load(f)

            # 检查是否是字典格式
            if isinstance(data, dict):
                print(f"检测到字典格式的tokenizer数据")

                # 使用字典数据重建BarTokenizer
                from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer

                tokenizer = BarTokenizer(
                    atr_period=data.get('atr_period', 14),
                    mapping_strategy=data.get('mapping_strategy_name', 'quantile'),
                    balancing_strategy=data.get('balancing_strategy_name', 'none'),
                    n_bins=data.get('n_bins', 100),
                    features=data.get('features', ['change', 'body', 'upper_shadow', 'lower_shadow'])
                )

                # 恢复状态
                tokenizer.feature_mappers = data.get('feature_mappers', {})
                tokenizer.is_fitted = data.get('is_fitted', False)
                tokenizer.vocab_size = data.get('vocab_size', 0)
                tokenizer.token_distribution = data.get('token_distribution', {})
                tokenizer.gini_coefficient = data.get('gini_coefficient', 0.0)
                tokenizer.mapping_strategy_name = data.get('mapping_strategy_name', 'quantile')
                tokenizer.balancing_strategy_name = data.get('balancing_strategy_name', 'none')

                print(f"✅ Tokenizer从字典数据重建成功")
                print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
                print(f"  特征列表: {tokenizer.features}")
                print(f"  映射策略: {tokenizer.mapping_strategy_name}")
                print(f"  是否已拟合: {tokenizer.is_fitted}")

                return tokenizer
            else:
                # 如果不是字典，可能是旧格式的BarTokenizer对象
                print(f"✅ Tokenizer加载成功（直接pickle格式）")
                print(f"  词汇表大小: {data.get_vocab_size()}")
                print(f"  特征列表: {data.features}")
                print(f"  映射策略: {getattr(data, 'mapping_strategy_name', 'unknown')}")

                return data

    except Exception as e:
        print(f"❌ Tokenizer加载失败: {e}")
        import traceback
        traceback.print_exc()
        raise


def load_pytorch_model(model_path, model_config):
    """加载PyTorch Lightning模型"""
    print(f"加载PyTorch模型: {model_path}")

    try:
        # 从checkpoint加载模型
        model = BarGpt4LightningModule.load_from_checkpoint(
            model_path,
            **model_config
        )
        model.eval()

        # 切换到推理模式
        if hasattr(model.model, 'inference_mode'):
            model.model.inference_mode()

        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)

        print(f"✅ PyTorch模型加载成功")
        print(f"  设备: {device}")

        return model, device
    except Exception as e:
        print(f"❌ PyTorch模型加载失败: {e}")
        raise


def load_onnx_model(model_path):
    """加载ONNX模型"""
    if not ONNX_AVAILABLE:
        raise ImportError("onnxruntime未安装，无法加载ONNX模型")

    print(f"加载ONNX模型: {model_path}")

    try:
        # 配置ONNX运行时选项
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
        session_options = ort.SessionOptions()
        session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        session_options.intra_op_num_threads = 4

        # 创建ONNX运行时会话
        session = ort.InferenceSession(
            model_path,
            providers=providers,
            sess_options=session_options
        )

        # 获取模型输入输出信息
        input_names = [input.name for input in session.get_inputs()]
        output_names = [output.name for output in session.get_outputs()]

        print(f"✅ ONNX模型加载成功")
        print(f"  输入: {input_names}")
        print(f"  输出: {output_names}")

        # 确定设备
        device = torch.device('cuda' if 'CUDAExecutionProvider' in session.get_providers() else 'cpu')
        print(f"  设备: {device}")

        # 创建ONNX模型包装器
        model = OnnxModelWrapper(session, device=device)

        return model, device
    except Exception as e:
        print(f"❌ ONNX模型加载失败: {e}")
        raise


def load_data(data_path, begin_date=None, end_date=None):
    """加载回测数据"""
    print(f"加载数据: {data_path}")

    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")

    try:
        # 根据文件扩展名选择加载方式
        if data_path.endswith('.parquet'):
            df = pd.read_parquet(data_path)
        elif data_path.endswith('.csv'):
            df = pd.read_csv(data_path)
        else:
            raise ValueError(f"不支持的文件格式: {data_path}")

        print(f"原始数据形状: {df.shape}")

        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        # 日期过滤
        if 'datetime' in df.columns and (begin_date or end_date):
            df['datetime'] = pd.to_datetime(df['datetime'])

            if begin_date:
                df = df[df['datetime'] >= begin_date]
            if end_date:
                df = df[df['datetime'] <= end_date]

            print(f"日期过滤后数据形状: {df.shape}")

        # 重置索引
        df = df.reset_index(drop=True)

        print(f"✅ 数据加载成功")
        print(f"  数据范围: {len(df)} 条记录")
        if 'datetime' in df.columns:
            print(f"  时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")

        return df
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        raise


def save_results(results, output_dir):
    """保存回测结果"""
    os.makedirs(output_dir, exist_ok=True)

    # 保存主要结果
    results_path = os.path.join(output_dir, 'backtest_results.json')

    # 准备可序列化的结果
    serializable_results = {
        'initial_capital': results['initial_capital'],
        'final_equity': results['final_equity'],
        'total_return': results['total_return'],
        'leverage': results['leverage'],
        'trades_count': len(results['trades']),
        'summary': {
            'total_trades': len(results['trades']),
            'profitable_trades': len([t for t in results['trades'] if t.get('pnl', 0) > 0]),
            'loss_trades': len([t for t in results['trades'] if t.get('pnl', 0) < 0]),
        }
    }

    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)

    # 保存详细交易记录
    if results['trades']:
        trades_df = pd.DataFrame(results['trades'])
        trades_path = os.path.join(output_dir, 'trades.csv')
        trades_df.to_csv(trades_path, index=False, encoding='utf-8')

    # 保存权益曲线
    if results['equity_curve']:
        equity_df = pd.DataFrame(results['equity_curve'])
        equity_path = os.path.join(output_dir, 'equity_curve.csv')
        equity_df.to_csv(equity_path, index=False, encoding='utf-8')

    # 保存信号历史
    if results['signals_history']:
        signals_df = pd.DataFrame(results['signals_history'])
        signals_path = os.path.join(output_dir, 'signals.csv')
        signals_df.to_csv(signals_path, index=False, encoding='utf-8')

    print(f"✅ 回测结果已保存到: {output_dir}")
    return results_path


def parse_args():
    """解析命令行参数"""
    parser = ArgumentParser(description='基于BarTokenizer训练模型的回测系统')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True,
                       help='模型文件路径（支持.ckpt和.onnx格式）')
    parser.add_argument('--tokenizer_path', type=str, required=True,
                       help='BarTokenizer文件路径（.pkl格式）')
    parser.add_argument('--config_path', type=str,
                       help='模型配置文件路径（PyTorch模型需要）')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True,
                       help='回测数据文件路径（支持.parquet和.csv格式）')
    parser.add_argument('--begin_date', type=str,
                       help='回测开始日期（YYYY-MM-DD格式）')
    parser.add_argument('--end_date', type=str,
                       help='回测结束日期（YYYY-MM-DD格式）')

    # 回测参数
    parser.add_argument('--initial_capital', type=float, default=10000.0,
                       help='初始资金')
    parser.add_argument('--seq_len', type=int, default=30,
                       help='序列长度')
    parser.add_argument('--commission', type=float, default=0.001,
                       help='交易手续费率')
    parser.add_argument('--threshold', type=float, default=0.6,
                       help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float,
                       help='止损比例（如0.05表示5%）')
    parser.add_argument('--take_profit', type=float,
                       help='止盈比例（如0.1表示10%）')
    parser.add_argument('--leverage', type=float, default=1.0,
                       help='交易杠杆倍数')

    # 模型推理参数
    parser.add_argument('--temperature', type=float, default=1.0,
                       help='温度参数，控制预测随机性')
    parser.add_argument('--top_k', type=int,
                       help='Top-K采样参数')
    parser.add_argument('--signal_type', type=str, default='threshold',
                       choices=['threshold', 'topk', 'momentum', 'ensemble'],
                       help='信号生成策略类型')

    # 输出参数
    parser.add_argument('--output_dir', type=str, default='backtest_results',
                       help='结果输出目录')
    parser.add_argument('--print_interval', type=int, default=100,
                       help='打印进度间隔')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    print("=== 基于BarTokenizer训练模型的回测系统 ===\n")

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    try:
        # 1. 加载Tokenizer
        tokenizer = load_tokenizer(args.tokenizer_path)

        # 2. 加载模型
        if args.model_path.endswith('.onnx'):
            # ONNX模型
            model, device = load_onnx_model(args.model_path)
        elif args.model_path.endswith('.ckpt'):
            # PyTorch Lightning模型
            if not args.config_path:
                raise ValueError("PyTorch模型需要提供config_path参数")

            # 加载配置
            with open(args.config_path, 'r', encoding='utf-8') as f:
                model_config = json.load(f)

            model, device = load_pytorch_model(args.model_path, model_config.get('model_args', {}))
        else:
            raise ValueError(f"不支持的模型格式: {args.model_path}")

        # 3. 加载数据
        df = load_data(args.data_path, args.begin_date, args.end_date)

        # 4. 创建回测器
        backtester = BarTokenizedModelBacktester(
            model=model,
            tokenizer=tokenizer,
            initial_capital=args.initial_capital,
            device=device,
            signal_type=args.signal_type,
            leverage=args.leverage
        )

        # 5. 执行回测
        print(f"\n开始回测...")
        results = backtester.backtest(
            df=df,
            seq_len=args.seq_len,
            commission=args.commission,
            threshold=args.threshold,
            stop_loss=args.stop_loss,
            take_profit=args.take_profit,
            temperature=args.temperature,
            top_k=args.top_k,
            print_interval=args.print_interval
        )

        # 6. 保存结果
        save_results(results, args.output_dir)

        # 7. 打印总结
        print(f"\n=== 回测总结 ===")
        print(f"初始资金: {results['initial_capital']:,.2f}")
        print(f"最终权益: {results['final_equity']:,.2f}")
        print(f"总收益率: {results['total_return']:.2%}")
        print(f"总交易次数: {len(results['trades'])}")

        if results['trades']:
            profitable_trades = [t for t in results['trades'] if t.get('pnl', 0) > 0]
            loss_trades = [t for t in results['trades'] if t.get('pnl', 0) < 0]

            print(f"盈利交易: {len(profitable_trades)} ({len(profitable_trades)/len(results['trades'])*100:.1f}%)")
            print(f"亏损交易: {len(loss_trades)} ({len(loss_trades)/len(results['trades'])*100:.1f}%)")

            if profitable_trades:
                avg_profit = np.mean([t['pnl'] for t in profitable_trades])
                print(f"平均盈利: {avg_profit:.2%}")

            if loss_trades:
                avg_loss = np.mean([t['pnl'] for t in loss_trades])
                print(f"平均亏损: {avg_loss:.2%}")

        print(f"\n回测完成！结果已保存到: {args.output_dir}")

    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
