"""
测试完整的ONNX导出流程（包括JSON配置保存）
"""

import os
import sys
import torch
import numpy as np
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_bar_gpt4_with_tokenizer import make_json_serializable
from bar_gpt4 import BarGpt4


def simulate_export_process():
    """模拟完整的导出过程"""
    print("=== 模拟完整的ONNX导出过程 ===\n")
    
    # 1. 创建模型
    print("1. 创建测试模型...")
    model = BarGpt4(
        block_size=30,
        code_size=100,
        vocab_size=500,
        n_layer=2,  # 使用较小的模型进行测试
        n_head=8,
        d_model=64,
        time_encoding='timeF',
        time_embed_type='time_feature',
        freq='t',
        pos_embed_type='rope',
        dropout=0.1
    )
    model.inference_mode()
    print(f"✅ 模型创建成功，参数数量: {model.get_num_params():,}")
    
    # 2. 模拟训练参数（包含Tensor）
    print(f"\n2. 创建模型配置（包含Tensor对象）...")
    class_weights = torch.tensor([1.0, 2.0, 3.0, 4.0, 5.0])  # 模拟类别权重
    
    model_args = {
        'block_size': 30,
        'code_size': 100,
        'vocab_size': 500,
        'n_layer': 2,
        'n_head': 8,
        'd_model': 64,
        'time_encoding': 'timeF',
        'time_embed_type': 'time_feature',
        'freq': 't',
        'pos_embed_type': 'rope',
        'dropout': 0.1,
        'lr': 0.0005,
        'weight_decay': 0.01,
        'class_weights': class_weights  # 这是导致原始问题的Tensor
    }
    
    print(f"✅ 配置创建成功，包含class_weights Tensor: {class_weights.shape}")
    
    # 3. 导出ONNX模型
    print(f"\n3. 导出ONNX模型...")
    onnx_dir = "test_export"
    os.makedirs(onnx_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_name = f"TestModel_{timestamp}"
    onnx_path = os.path.join(onnx_dir, f"{model_name}.onnx")
    
    success = model.export_onnx(
        save_path=onnx_path,
        batch_size=1,
        seq_len=30,
        dynamic_axes=True,
        opset_version=14
    )
    
    if not success:
        print("❌ ONNX导出失败")
        return False
    
    print(f"✅ ONNX模型导出成功: {onnx_path}")
    
    # 4. 保存配置文件（这里是原来出错的地方）
    print(f"\n4. 保存模型配置文件...")
    config_path = os.path.join(onnx_dir, f"{model_name}_config.json")
    
    # 模拟完整的配置结构
    model_config = {
        'model_name': model_name,
        'export_time': timestamp,
        'best_fold': 0,
        'best_score': 1.234,
        'model_args': model_args,  # 包含Tensor的配置
        'tokenizer_config': {
            'mapping_strategy': 'quantile',
            'balancing_strategy': 'frequency',
            'n_bins': 30,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
            'combination_method': 'hierarchical',
            'max_token_frequency': 0.1,
            'gini_threshold': 0.7
        },
        'dataset_info': {
            'vocab_size': 500,
            'code_size': 100,
            'sample_count': 10000
        }
    }
    
    try:
        # 使用修复后的序列化函数
        serializable_config = make_json_serializable(model_config)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置文件保存成功: {config_path}")
        
        # 验证保存的配置
        with open(config_path, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        print(f"✅ 配置文件验证成功")
        print(f"   - class_weights已转换为: {type(loaded_config['model_args']['class_weights'])}")
        print(f"   - 值: {loaded_config['model_args']['class_weights']}")
        
    except Exception as e:
        print(f"❌ 配置文件保存失败: {e}")
        return False
    
    # 5. 测试ONNX推理
    print(f"\n5. 测试ONNX推理...")
    try:
        # 创建测试输入
        batch_size = 1
        seq_len = 30
        code = torch.randint(0, 100, (batch_size, seq_len))
        x = torch.randint(0, 500, (batch_size, seq_len))
        x_mark = torch.randn(batch_size, seq_len, 5)  # freq='t'需要5个特征
        
        # PyTorch推理
        with torch.no_grad():
            pytorch_output, _ = model(code, x, x_mark)
        
        # ONNX推理
        onnx_output = model.inference_with_onnx(onnx_path, code, x, x_mark)
        
        # 比较结果
        diff = torch.abs(pytorch_output - onnx_output).max().item()
        print(f"✅ ONNX推理成功")
        print(f"   - PyTorch输出形状: {pytorch_output.shape}")
        print(f"   - ONNX输出形状: {onnx_output.shape}")
        print(f"   - 最大差异: {diff:.6f}")
        
        if diff < 1e-4:
            print(f"✅ PyTorch和ONNX输出一致")
        else:
            print(f"⚠️  PyTorch和ONNX输出存在差异")
        
    except Exception as e:
        print(f"❌ ONNX推理失败: {e}")
        return False
    
    # 6. 清理测试文件
    print(f"\n6. 清理测试文件...")
    try:
        os.remove(onnx_path)
        os.remove(config_path)
        os.rmdir(onnx_dir)
        print(f"✅ 测试文件清理完成")
    except Exception as e:
        print(f"⚠️  清理文件时出错: {e}")
    
    return True


def test_real_scenario():
    """测试真实场景中的配置"""
    print(f"\n=== 测试真实训练场景配置 ===")
    
    # 模拟真实训练后的配置
    real_config = {
        'model_name': 'BarGpt4_Tokenized_quantile_30bins_20250531_145311',
        'export_time': '20250531_145311',
        'best_fold': 1,
        'best_score': 1.8005,
        'model_args': {
            'block_size': 30,
            'code_size': 100,
            'vocab_size': 500,
            'n_layer': 4,
            'n_head': 32,
            'd_model': 96,
            'time_encoding': 'timeF',
            'time_embed_type': 'time_feature',
            'freq': 't',
            'pos_embed_type': 'rope',
            'dropout': 0.1,
            'lr': 0.0005,
            'weight_decay': 0.01,
            'class_weights': torch.randn(500)  # 真实大小的类别权重
        },
        'tokenizer_config': {
            'mapping_strategy': 'quantile',
            'balancing_strategy': 'frequency',
            'n_bins': 30,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
            'combination_method': 'hierarchical',
            'max_token_frequency': 0.1,
            'gini_threshold': 0.7
        },
        'dataset_info': {
            'vocab_size': 500,
            'code_size': 100,
            'sample_count': 50000
        }
    }
    
    try:
        # 测试序列化
        serializable_config = make_json_serializable(real_config)
        json_str = json.dumps(serializable_config, indent=2, ensure_ascii=False)
        
        print(f"✅ 真实场景配置序列化成功")
        print(f"   - JSON字符串长度: {len(json_str):,} 字符")
        print(f"   - class_weights转换为长度: {len(serializable_config['model_args']['class_weights'])}")
        
        # 测试文件保存
        test_file = "real_config_test.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(json_str)
        
        # 验证加载
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        print(f"✅ 真实场景配置文件操作成功")
        
        # 清理
        os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("开始测试完整的ONNX导出流程...")
    
    # 测试完整导出过程
    success1 = simulate_export_process()
    
    # 测试真实场景
    success2 = test_real_scenario()
    
    # 总结
    print(f"\n" + "="*60)
    print("=== 测试总结 ===")
    print(f"完整导出流程: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"真实场景测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"\n🎉 完整的ONNX导出流程修复成功！")
        print(f"\n解决的问题:")
        print(f"  1. ✅ ONNX模型导出正常")
        print(f"  2. ✅ JSON配置保存不再出错")
        print(f"  3. ✅ PyTorch Tensor自动转换为列表")
        print(f"  4. ✅ 支持大型模型配置")
        print(f"  5. ✅ ONNX推理功能正常")
        print(f"\n现在可以安全地使用 --export_onnx 参数进行模型导出了！")
    else:
        print(f"\n❌ 导出流程仍存在问题，需要进一步调试")
