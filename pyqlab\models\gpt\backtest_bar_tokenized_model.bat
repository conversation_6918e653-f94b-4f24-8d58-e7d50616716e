@echo off
e:
cd e:\lab\RoboQuant\pylab

@REM 注意：需要先训练模型并导出ONNX格式才能运行回测
@REM 当前示例仅用于测试tokenizer加载功能

@REM 示例1：使用ONNX模型进行回测（需要先有训练好的模型）
@REM python .\pyqlab\models\gpt\backtest_bar_tokenized_model.py ^
@REM --model_path ./models/lightning_logs/onnx_models/BarGpt4_Tokenized_30bins.onnx ^
@REM --tokenizer_path E:/lab/RoboQuant/pylab/pyqlab/models/gpt/tokenizer_quantile_30.pkl ^
@REM --data_path f:/hqdata/fut_top_min1.parquet ^
@REM --begin_date 2025-04-21 ^
@REM --end_date 2025-12-31 ^
@REM --initial_capital 100000 ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --threshold 0.6 ^
@REM --stop_loss 0.05 ^
@REM --take_profit 0.1 ^
@REM --leverage 1.0 ^
@REM --temperature 1.0 ^
@REM --signal_type threshold ^
@REM --output_dir backtest_results_tokenized ^
@REM --print_interval 100 ^
@REM --seed 42

echo "回测系统已准备就绪！"
echo "请先训练BarTokenizer模型并导出ONNX格式，然后修改上面的model_path参数"
echo "当前可用的tokenizer: E:/lab/RoboQuant/pylab/pyqlab/models/gpt/tokenizer_quantile_30.pkl"
echo "词汇表大小: 2700, 特征: body, upper_shadow, lower_shadow"

pause

@REM exam2: PyTorch Lightning checkpoint backtest
@REM python .\pyqlab\models\gpt\backtest_bar_tokenized_model.py ^
@REM --model_path lightning_logs_tokenized/BarGpt4_Tokenized_Fold0/checkpoints/best-epoch=05-val_loss=3.792.ckpt ^
@REM --tokenizer_path tokenizer_quantile_100.pkl ^
@REM --config_path lightning_logs_tokenized/onnx_models/BarGpt4_Tokenized_quantile_100bins_20250101_120000_config.json ^
@REM --data_path f:/hqdata/fut_top_min1.parquet ^
@REM --begin_date 2025-04-01 ^
@REM --end_date 2025-12-31 ^
@REM --initial_capital 100000 ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --threshold 0.6 ^
@REM --stop_loss 0.05 ^
@REM --take_profit 0.1 ^
@REM --leverage 1.0 ^
@REM --temperature 1.0 ^
@REM --signal_type ensemble ^
@REM --output_dir backtest_results_tokenized_pytorch ^
@REM --print_interval 100 ^
@REM --seed 42



