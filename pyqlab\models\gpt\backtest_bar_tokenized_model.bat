@echo off
e:
cd e:\lab\RoboQuant\pylab

python .\pyqlab\models\gpt\backtest_bar_tokenized_model.py ^
--model_path ./models/lightning_logs/onnx_models/BarGpt4_Tokenized_30bins.onnx ^
--tokenizer_path E:/lab/RoboQuant/pylab/pyqlab/models/gpt/tokenizer_quantile_30.pkl ^
--data_path f:/hqdata/fut_top_min1.parquet ^
--begin_date 2025-04-21 ^
--end_date 2025-12-31 ^
--initial_capital 100000 ^
--seq_len 30 ^
--commission 0.001 ^
--threshold 0.6 ^
--stop_loss 0.05 ^
--take_profit 0.1 ^
--leverage 1.0 ^
--temperature 1.0 ^
--signal_type threshold ^
--output_dir backtest_results_tokenized ^
--print_interval 100 ^
--seed 42

@REM exam2: PyTorch Lightning checkpoint backtest
@REM python .\pyqlab\models\gpt\backtest_bar_tokenized_model.py ^
@REM --model_path lightning_logs_tokenized/BarGpt4_Tokenized_Fold0/checkpoints/best-epoch=05-val_loss=3.792.ckpt ^
@REM --tokenizer_path tokenizer_quantile_100.pkl ^
@REM --config_path lightning_logs_tokenized/onnx_models/BarGpt4_Tokenized_quantile_100bins_20250101_120000_config.json ^
@REM --data_path f:/hqdata/fut_top_min1.parquet ^
@REM --begin_date 2025-04-01 ^
@REM --end_date 2025-12-31 ^
@REM --initial_capital 100000 ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --threshold 0.6 ^
@REM --stop_loss 0.05 ^
@REM --take_profit 0.1 ^
@REM --leverage 1.0 ^
@REM --temperature 1.0 ^
@REM --signal_type ensemble ^
@REM --output_dir backtest_results_tokenized_pytorch ^
@REM --print_interval 100 ^
@REM --seed 42



