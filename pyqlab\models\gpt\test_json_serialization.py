"""
测试JSON序列化修复是否有效
"""

import torch
import numpy as np
import json
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_bar_gpt4_with_tokenizer import make_json_serializable


def test_json_serialization():
    """测试JSON序列化功能"""
    print("=== 测试JSON序列化修复 ===\n")
    
    # 创建包含各种不可序列化对象的测试数据
    test_data = {
        'model_name': 'test_model',
        'export_time': '20250531_145311',
        'best_fold': 0,
        'best_score': 1.234,
        'model_args': {
            'block_size': 30,
            'code_size': 100,
            'vocab_size': 500,
            'n_layer': 4,
            'n_head': 32,
            'd_model': 96,
            'time_encoding': 'timeF',
            'time_embed_type': 'time_feature',
            'freq': 't',
            'pos_embed_type': 'rope',
            'dropout': 0.1,
            'lr': 0.0005,
            'weight_decay': 0.01,
            'class_weights': torch.tensor([1.0, 2.0, 3.0, 4.0, 5.0])  # 这是导致问题的Tensor
        },
        'tokenizer_config': {
            'mapping_strategy': 'quantile',
            'balancing_strategy': 'frequency',
            'n_bins': 30,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
            'combination_method': 'hierarchical',
            'max_token_frequency': 0.1,
            'gini_threshold': 0.7
        },
        'dataset_info': {
            'vocab_size': 500,
            'code_size': 100,
            'sample_count': 10000
        },
        'numpy_array': np.array([1, 2, 3, 4, 5]),  # 测试numpy数组
        'numpy_int': np.int64(42),  # 测试numpy整数
        'numpy_float': np.float32(3.14),  # 测试numpy浮点数
        'nested_tensor': {
            'weights': torch.randn(3, 3),
            'bias': torch.zeros(3)
        }
    }
    
    print("原始数据结构:")
    print(f"  - class_weights类型: {type(test_data['model_args']['class_weights'])}")
    print(f"  - numpy_array类型: {type(test_data['numpy_array'])}")
    print(f"  - numpy_int类型: {type(test_data['numpy_int'])}")
    print(f"  - numpy_float类型: {type(test_data['numpy_float'])}")
    print(f"  - nested_tensor.weights类型: {type(test_data['nested_tensor']['weights'])}")
    
    # 测试原始JSON序列化（应该失败）
    print(f"\n=== 测试原始JSON序列化 ===")
    try:
        json.dumps(test_data)
        print("❌ 原始JSON序列化意外成功")
    except TypeError as e:
        print(f"✅ 原始JSON序列化失败（预期）: {e}")
    
    # 测试修复后的序列化
    print(f"\n=== 测试修复后的序列化 ===")
    try:
        serializable_data = make_json_serializable(test_data)
        json_str = json.dumps(serializable_data, indent=2, ensure_ascii=False)
        print("✅ 修复后的序列化成功")
        
        # 检查转换结果
        print(f"\n转换后的数据类型:")
        print(f"  - class_weights类型: {type(serializable_data['model_args']['class_weights'])}")
        print(f"  - numpy_array类型: {type(serializable_data['numpy_array'])}")
        print(f"  - numpy_int类型: {type(serializable_data['numpy_int'])}")
        print(f"  - numpy_float类型: {type(serializable_data['numpy_float'])}")
        print(f"  - nested_tensor.weights类型: {type(serializable_data['nested_tensor']['weights'])}")
        
        # 验证数据内容
        print(f"\n数据内容验证:")
        print(f"  - class_weights值: {serializable_data['model_args']['class_weights']}")
        print(f"  - numpy_array值: {serializable_data['numpy_array']}")
        print(f"  - numpy_int值: {serializable_data['numpy_int']}")
        print(f"  - numpy_float值: {serializable_data['numpy_float']}")
        
        return True, json_str
        
    except Exception as e:
        print(f"❌ 修复后的序列化失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_file_save_load():
    """测试文件保存和加载"""
    print(f"\n=== 测试文件保存和加载 ===")
    
    success, json_str = test_json_serialization()
    if not success:
        return False
    
    # 保存到文件
    test_file = "test_config.json"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(json_str)
        print(f"✅ 成功保存到文件: {test_file}")
        
        # 从文件加载
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        print(f"✅ 成功从文件加载")
        
        # 验证加载的数据
        print(f"加载的数据验证:")
        print(f"  - model_name: {loaded_data['model_name']}")
        print(f"  - class_weights: {loaded_data['model_args']['class_weights']}")
        print(f"  - vocab_size: {loaded_data['dataset_info']['vocab_size']}")
        
        # 清理测试文件
        os.remove(test_file)
        print(f"✅ 清理测试文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        # 尝试清理文件
        try:
            os.remove(test_file)
        except:
            pass
        return False


def test_edge_cases():
    """测试边界情况"""
    print(f"\n=== 测试边界情况 ===")
    
    edge_cases = {
        'none_tensor': None,
        'empty_tensor': torch.tensor([]),
        'scalar_tensor': torch.tensor(42),
        'complex_nested': {
            'level1': {
                'level2': {
                    'tensor': torch.randn(2, 2),
                    'array': np.array([[1, 2], [3, 4]]),
                    'mixed_list': [torch.tensor(1), np.array(2), 3, "string"]
                }
            }
        },
        'list_of_tensors': [torch.tensor(i) for i in range(3)],
        'tuple_of_arrays': (np.array([1, 2]), np.array([3, 4]))
    }
    
    try:
        serializable_cases = make_json_serializable(edge_cases)
        json_str = json.dumps(serializable_cases, indent=2)
        print("✅ 边界情况序列化成功")
        
        print(f"转换结果:")
        print(f"  - none_tensor: {serializable_cases['none_tensor']}")
        print(f"  - empty_tensor: {serializable_cases['empty_tensor']}")
        print(f"  - scalar_tensor: {serializable_cases['scalar_tensor']}")
        print(f"  - complex_nested深度: {len(serializable_cases['complex_nested']['level1']['level2'])}")
        print(f"  - list_of_tensors: {serializable_cases['list_of_tensors']}")
        print(f"  - tuple_of_arrays: {serializable_cases['tuple_of_arrays']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("开始测试JSON序列化修复...")
    
    # 测试基本序列化
    success1, _ = test_json_serialization()
    
    # 测试文件操作
    success2 = test_file_save_load()
    
    # 测试边界情况
    success3 = test_edge_cases()
    
    # 总结
    print(f"\n" + "="*60)
    print("=== 测试总结 ===")
    print(f"基本序列化测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"文件操作测试: {'✅ 通过' if success2 else '❌ 失败'}")
    print(f"边界情况测试: {'✅ 通过' if success3 else '❌ 失败'}")
    
    if success1 and success2 and success3:
        print(f"\n🎉 JSON序列化修复成功！")
        print(f"现在可以安全地保存包含PyTorch Tensor的模型配置了")
        print(f"\n修复的问题:")
        print(f"  1. ✅ PyTorch Tensor自动转换为列表")
        print(f"  2. ✅ NumPy数组自动转换为列表")
        print(f"  3. ✅ NumPy标量自动转换为Python标量")
        print(f"  4. ✅ 嵌套结构递归处理")
        print(f"  5. ✅ 支持复杂的数据结构")
    else:
        print(f"\n❌ JSON序列化修复仍存在问题，需要进一步调试")
